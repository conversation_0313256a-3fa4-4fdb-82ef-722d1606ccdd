import helmet from 'helmet';
import { Request, Response, NextFunction } from 'express';
import rateLimit from 'express-rate-limit';

/**
 * Security middleware configuration for production hardening
 */

// Content Security Policy configuration
const cspConfig = {
  directives: {
    defaultSrc: ["'self'"],
    scriptSrc: [
      "'self'",
      "'unsafe-inline'", // Required for some admin interfaces
      "https://cdn.jsdelivr.net",
      "https://unpkg.com"
    ],
    styleSrc: [
      "'self'",
      "'unsafe-inline'", // Required for dynamic styling
      "https://fonts.googleapis.com",
      "https://cdn.jsdelivr.net"
    ],
    fontSrc: [
      "'self'",
      "https://fonts.gstatic.com",
      "data:"
    ],
    imgSrc: [
      "'self'",
      "data:",
      "https:",
      "blob:"
    ],
    connectSrc: [
      "'self'",
      "https://api.supabase.co",
      "https://*.supabase.co",
      "wss://*.supabase.co"
    ],
    frameSrc: ["'none'"],
    objectSrc: ["'none'"],
    mediaSrc: ["'self'"],
    manifestSrc: ["'self'"],
    workerSrc: ["'self'", "blob:"],
    upgradeInsecureRequests: []
  },
  reportOnly: false
};

// Rate limiting configuration
export const createRateLimiter = (windowMs: number, max: number, message?: string) => {
  return rateLimit({
    windowMs,
    max,
    message: message || 'Too many requests from this IP, please try again later.',
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req: Request, res: Response) => {
      res.status(429).json({
        success: false,
        error: 'Rate limit exceeded',
        message: 'Too many requests from this IP, please try again later.',
        retryAfter: Math.round(windowMs / 1000)
      });
    }
  });
};

// General rate limiter (100 requests per 15 minutes)
export const generalRateLimit = createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  100,
  'Too many requests from this IP, please try again in 15 minutes.'
);

// Strict rate limiter for sensitive endpoints (10 requests per 15 minutes)
export const strictRateLimit = createRateLimiter(
  15 * 60 * 1000, // 15 minutes
  10,
  'Too many requests to sensitive endpoint, please try again in 15 minutes.'
);

// API rate limiter (1000 requests per hour)
export const apiRateLimit = createRateLimiter(
  60 * 60 * 1000, // 1 hour
  1000,
  'API rate limit exceeded, please try again in an hour.'
);

// Helmet configuration for security headers
export const helmetConfig = helmet({
  // Content Security Policy
  contentSecurityPolicy: cspConfig,
  
  // DNS Prefetch Control
  dnsPrefetchControl: {
    allow: false
  },
  
  // Frame Options (prevent clickjacking)
  frameguard: {
    action: 'deny'
  },
  
  // Hide Powered-By header
  hidePoweredBy: true,
  
  // HTTP Strict Transport Security
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true
  },
  
  // IE No Open
  ieNoOpen: true,
  
  // Don't Sniff Mimetype
  noSniff: true,
  
  // Origin Agent Cluster
  originAgentCluster: true,
  
  // Permitted Cross-Domain Policies
  permittedCrossDomainPolicies: false,
  
  // Referrer Policy
  referrerPolicy: {
    policy: ['no-referrer', 'strict-origin-when-cross-origin']
  },
  
  // X-XSS-Protection
  xssFilter: true
});

// Secret stripping patterns for AI prompts
const SECRET_PATTERNS = [
  // API Keys
  /sk-[a-zA-Z0-9]{48}/gi, // OpenAI API keys
  /sk-ant-[a-zA-Z0-9-]{95}/gi, // Anthropic API keys
  /ghp_[a-zA-Z0-9]{36}/gi, // GitHub Personal Access Tokens
  /gho_[a-zA-Z0-9]{36}/gi, // GitHub OAuth tokens
  /ghu_[a-zA-Z0-9]{36}/gi, // GitHub User tokens
  /ghs_[a-zA-Z0-9]{36}/gi, // GitHub Server tokens
  /ghr_[a-zA-Z0-9]{36}/gi, // GitHub Refresh tokens
  
  // AWS Keys
  /AKIA[0-9A-Z]{16}/gi, // AWS Access Key ID
  /[0-9a-zA-Z/+]{40}/gi, // AWS Secret Access Key (basic pattern)
  
  // Database URLs
  /postgresql:\/\/[^:]+:[^@]+@[^\/]+\/[^\s]+/gi,
  /postgres:\/\/[^:]+:[^@]+@[^\/]+\/[^\s]+/gi,
  
  // Generic secrets (common patterns)
  /[a-zA-Z0-9]{32,}/gi, // Long alphanumeric strings (potential secrets)
  
  // Environment variable patterns
  /[A-Z_]+_KEY\s*=\s*[^\s]+/gi,
  /[A-Z_]+_SECRET\s*=\s*[^\s]+/gi,
  /[A-Z_]+_TOKEN\s*=\s*[^\s]+/gi,
  /[A-Z_]+_PASSWORD\s*=\s*[^\s]+/gi,
  
  // JWT tokens
  /eyJ[a-zA-Z0-9_-]*\.[a-zA-Z0-9_-]*\.[a-zA-Z0-9_-]*/gi,
  
  // Slack webhooks
  /https:\/\/hooks\.slack\.com\/services\/[A-Z0-9]+\/[A-Z0-9]+\/[a-zA-Z0-9]+/gi
];

/**
 * Strip secrets from text before sending to AI services
 */
export const stripSecrets = (text: string): string => {
  let cleanText = text;
  
  SECRET_PATTERNS.forEach(pattern => {
    cleanText = cleanText.replace(pattern, '[REDACTED]');
  });
  
  return cleanText;
};

/**
 * Middleware to strip secrets from request body before AI processing
 */
export const secretStrippingMiddleware = (req: Request, res: Response, next: NextFunction) => {
  if (req.body && typeof req.body === 'object') {
    // Recursively strip secrets from request body
    const stripFromObject = (obj: any): any => {
      if (typeof obj === 'string') {
        return stripSecrets(obj);
      } else if (Array.isArray(obj)) {
        return obj.map(stripFromObject);
      } else if (obj && typeof obj === 'object') {
        const cleaned: any = {};
        for (const [key, value] of Object.entries(obj)) {
          cleaned[key] = stripFromObject(value);
        }
        return cleaned;
      }
      return obj;
    };
    
    req.body = stripFromObject(req.body);
  }
  
  next();
};

/**
 * Input validation middleware
 */
export const inputValidationMiddleware = (req: Request, res: Response, next: NextFunction) => {
  // Check for common injection patterns
  const suspiciousPatterns = [
    /<script[^>]*>.*?<\/script>/gi,
    /javascript:/gi,
    /on\w+\s*=/gi,
    /eval\s*\(/gi,
    /expression\s*\(/gi,
    /vbscript:/gi,
    /data:text\/html/gi
  ];
  
  const checkValue = (value: any): boolean => {
    if (typeof value === 'string') {
      return suspiciousPatterns.some(pattern => pattern.test(value));
    } else if (Array.isArray(value)) {
      return value.some(checkValue);
    } else if (value && typeof value === 'object') {
      return Object.values(value).some(checkValue);
    }
    return false;
  };
  
  // Check request body
  if (req.body && checkValue(req.body)) {
    return res.status(400).json({
      success: false,
      error: 'Invalid input detected',
      message: 'Request contains potentially malicious content'
    });
  }
  
  // Check query parameters
  if (req.query && checkValue(req.query)) {
    return res.status(400).json({
      success: false,
      error: 'Invalid input detected',
      message: 'Query parameters contain potentially malicious content'
    });
  }
  
  next();
};

/**
 * Request logging middleware for security monitoring
 */
export const securityLoggingMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const startTime = Date.now();
  
  // Log suspicious requests
  const suspiciousIndicators = [
    req.path.includes('..'),
    req.path.includes('<script'),
    req.path.includes('javascript:'),
    req.get('User-Agent')?.includes('sqlmap'),
    req.get('User-Agent')?.includes('nikto'),
    req.get('User-Agent')?.includes('nmap')
  ];
  
  if (suspiciousIndicators.some(Boolean)) {
    console.warn('Suspicious request detected:', {
      ip: req.ip,
      method: req.method,
      path: req.path,
      userAgent: req.get('User-Agent'),
      timestamp: new Date().toISOString()
    });
  }
  
  // Override res.end to log response time and status
  const originalEnd = res.end;
  res.end = function(chunk?: any, encoding?: any) {
    const duration = Date.now() - startTime;
    
    // Log slow requests (potential DoS attempts)
    if (duration > 5000) {
      console.warn('Slow request detected:', {
        ip: req.ip,
        method: req.method,
        path: req.path,
        duration,
        status: res.statusCode,
        timestamp: new Date().toISOString()
      });
    }
    
    originalEnd.call(this, chunk, encoding);
  };
  
  next();
};
