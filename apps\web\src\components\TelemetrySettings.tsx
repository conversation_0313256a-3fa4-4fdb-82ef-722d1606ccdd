import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Switch } from '@/components/ui/switch';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Label } from '@/components/ui/label';
import { BarChart3, Shield, Eye, Download, Trash2 } from 'lucide-react';
import { telemetry } from '@/lib/telemetry';
import { supabase } from '@/lib/supabase';
import { toast } from 'sonner';

interface TelemetryStats {
  total_events: number;
  events_this_week: number;
  most_used_features: Array<{ feature: string; count: number }>;
  last_activity: string;
}

export const TelemetrySettings = () => {
  const [isEnabled, setIsEnabled] = useState(telemetry.isEnabled());
  const [stats, setStats] = useState<TelemetryStats | null>(null);
  const [loading, setLoading] = useState(false);
  const [exporting, setExporting] = useState(false);
  const [deleting, setDeleting] = useState(false);

  useEffect(() => {
    loadTelemetryStats();
  }, []);

  const loadTelemetryStats = async () => {
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) return;

      // Get total events count
      const { count: totalEvents } = await supabase
        .from('telemetry_events')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id);

      // Get events from this week
      const weekAgo = new Date();
      weekAgo.setDate(weekAgo.getDate() - 7);
      
      const { count: weekEvents } = await supabase
        .from('telemetry_events')
        .select('*', { count: 'exact', head: true })
        .eq('user_id', user.id)
        .gte('created_at', weekAgo.toISOString());

      // Get most used features
      const { data: featureData } = await supabase
        .from('telemetry_events')
        .select('event_data')
        .eq('user_id', user.id)
        .eq('event_type', 'feature_used')
        .limit(100);

      const featureCounts: Record<string, number> = {};
      featureData?.forEach(event => {
        const feature = event.event_data?.feature;
        if (feature) {
          featureCounts[feature] = (featureCounts[feature] || 0) + 1;
        }
      });

      const mostUsedFeatures = Object.entries(featureCounts)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 5)
        .map(([feature, count]) => ({ feature, count }));

      // Get last activity
      const { data: lastActivity } = await supabase
        .from('telemetry_events')
        .select('created_at')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })
        .limit(1)
        .single();

      setStats({
        total_events: totalEvents || 0,
        events_this_week: weekEvents || 0,
        most_used_features: mostUsedFeatures,
        last_activity: lastActivity?.created_at || '',
      });
    } catch (error) {
      console.error('Error loading telemetry stats:', error);
    }
  };

  const handleToggleTelemetry = async (enabled: boolean) => {
    setLoading(true);
    try {
      await telemetry.setTelemetryEnabled(enabled);
      setIsEnabled(enabled);
      
      if (enabled) {
        await telemetry.trackFeatureUsed('telemetry_enabled');
        toast.success('Telemetry enabled. Thank you for helping improve Metamorphic Reactor!');
      } else {
        toast.success('Telemetry disabled. Your privacy is respected.');
      }
    } catch (error) {
      console.error('Error updating telemetry settings:', error);
      toast.error('Failed to update telemetry settings');
    } finally {
      setLoading(false);
    }
  };

  const handleExportData = async () => {
    setExporting(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast.error('Please sign in to export data');
        return;
      }

      const { data, error } = await supabase
        .from('telemetry_events')
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });

      if (error) throw error;

      // Create and download JSON file
      const blob = new Blob([JSON.stringify(data, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `metamorphic-reactor-telemetry-${new Date().toISOString().split('T')[0]}.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      await telemetry.trackFeatureUsed('telemetry_export');
      toast.success('Telemetry data exported successfully');
    } catch (error) {
      console.error('Error exporting telemetry data:', error);
      toast.error('Failed to export telemetry data');
    } finally {
      setExporting(false);
    }
  };

  const handleDeleteData = async () => {
    if (!confirm('Are you sure you want to delete all your telemetry data? This action cannot be undone.')) {
      return;
    }

    setDeleting(true);
    try {
      const { data: { user } } = await supabase.auth.getUser();
      if (!user) {
        toast.error('Please sign in to delete data');
        return;
      }

      const { error } = await supabase
        .from('telemetry_events')
        .delete()
        .eq('user_id', user.id);

      if (error) throw error;

      setStats({
        total_events: 0,
        events_this_week: 0,
        most_used_features: [],
        last_activity: '',
      });

      toast.success('All telemetry data has been deleted');
    } catch (error) {
      console.error('Error deleting telemetry data:', error);
      toast.error('Failed to delete telemetry data');
    } finally {
      setDeleting(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Main Telemetry Toggle */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-white">
            <BarChart3 className="w-5 h-5" />
            <span>Usage Analytics</span>
            {isEnabled ? (
              <Badge className="bg-green-600/20 text-green-300 border-green-500/30">
                Enabled
              </Badge>
            ) : (
              <Badge className="bg-slate-600/20 text-slate-300 border-slate-500/30">
                Disabled
              </Badge>
            )}
          </CardTitle>
          <CardDescription className="text-slate-400">
            Help improve Metamorphic Reactor by sharing anonymous usage data
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-1">
              <Label className="text-slate-300">Enable telemetry</Label>
              <p className="text-sm text-slate-500">
                Share anonymous usage data to help us improve the product
              </p>
            </div>
            <Switch
              checked={isEnabled}
              onCheckedChange={handleToggleTelemetry}
              disabled={loading}
            />
          </div>

          {isEnabled && (
            <div className="bg-slate-900/50 rounded-lg p-4">
              <h4 className="text-sm font-medium text-slate-300 mb-2">What we collect:</h4>
              <ul className="text-sm text-slate-400 space-y-1">
                <li>• Feature usage and interaction patterns</li>
                <li>• Performance metrics and error reports</li>
                <li>• Reactor loop statistics (without prompt content)</li>
                <li>• General application usage data</li>
              </ul>
              <p className="text-xs text-slate-500 mt-3">
                We never collect personal information, API keys, or code content.
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Telemetry Statistics */}
      {isEnabled && stats && (
        <Card className="bg-slate-800/50 border-slate-700">
          <CardHeader>
            <CardTitle className="flex items-center space-x-2 text-white">
              <Eye className="w-5 h-5" />
              <span>Your Usage Statistics</span>
            </CardTitle>
            <CardDescription className="text-slate-400">
              Overview of your telemetry data
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="bg-slate-900/50 rounded-lg p-3">
                <p className="text-sm text-slate-400">Total Events</p>
                <p className="text-2xl font-bold text-white">{stats.total_events}</p>
              </div>
              <div className="bg-slate-900/50 rounded-lg p-3">
                <p className="text-sm text-slate-400">This Week</p>
                <p className="text-2xl font-bold text-white">{stats.events_this_week}</p>
              </div>
            </div>

            {stats.most_used_features.length > 0 && (
              <div>
                <h4 className="text-sm font-medium text-slate-300 mb-2">Most Used Features</h4>
                <div className="space-y-2">
                  {stats.most_used_features.map(({ feature, count }) => (
                    <div key={feature} className="flex items-center justify-between text-sm">
                      <span className="text-slate-300 capitalize">{feature.replace('_', ' ')}</span>
                      <Badge variant="outline" className="border-slate-600 text-slate-300">
                        {count}
                      </Badge>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {stats.last_activity && (
              <div>
                <p className="text-sm text-slate-400">
                  Last activity: {new Date(stats.last_activity).toLocaleDateString()}
                </p>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Data Management */}
      <Card className="bg-slate-800/50 border-slate-700">
        <CardHeader>
          <CardTitle className="flex items-center space-x-2 text-white">
            <Shield className="w-5 h-5" />
            <span>Data Management</span>
          </CardTitle>
          <CardDescription className="text-slate-400">
            Control your telemetry data
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex flex-col sm:flex-row gap-3">
            <Button
              variant="outline"
              onClick={handleExportData}
              disabled={exporting || !isEnabled}
              className="border-slate-600 text-slate-300 hover:bg-slate-700"
            >
              <Download className="w-4 h-4 mr-2" />
              {exporting ? 'Exporting...' : 'Export Data'}
            </Button>
            <Button
              variant="outline"
              onClick={handleDeleteData}
              disabled={deleting || !isEnabled}
              className="border-red-600 text-red-300 hover:bg-red-900/20"
            >
              <Trash2 className="w-4 h-4 mr-2" />
              {deleting ? 'Deleting...' : 'Delete All Data'}
            </Button>
          </div>
          <p className="text-xs text-slate-500">
            You can export or delete your telemetry data at any time. 
            Exported data includes all events we've collected about your usage.
          </p>
        </CardContent>
      </Card>
    </div>
  );
};
