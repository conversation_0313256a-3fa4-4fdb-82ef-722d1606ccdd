import { describe, it, expect, jest, beforeEach, afterEach } from '@jest/globals';

// Mock fetch globally
global.fetch = jest.fn();

// Mock Supabase
const mockSupabase = {
  from: jest.fn(() => ({
    insert: jest.fn(() => ({ select: jest.fn(() => ({ single: jest.fn() })) })),
    update: jest.fn(() => ({ eq: jest.fn() })),
  })),
};

jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => mockSupabase),
}));

describe('AI Loop Integration Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    (fetch as jest.MockedFunction<typeof fetch>).mockClear();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('GPT-4 Integration', () => {
    it('should successfully call GPT-4 API', async () => {
      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          choices: [{ message: { content: '{"operations": [], "description": "test", "confidence": 0.8}' } }],
          usage: { total_tokens: 100 },
        }),
      };

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue(mockResponse as any);

      // Simulate the GPT-4 call logic
      const prompt = 'Test prompt for code transformation';
      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: {
          'Authorization': 'Bearer test-key',
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          model: 'gpt-4-turbo-preview',
          messages: [{ role: 'user', content: prompt }],
          max_tokens: 2000,
          temperature: 0.7,
        }),
      });

      expect(response.ok).toBe(true);
      expect(fetch).toHaveBeenCalledWith(
        'https://api.openai.com/v1/chat/completions',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Authorization': 'Bearer test-key',
            'Content-Type': 'application/json',
          }),
        })
      );

      const data = await response.json();
      expect(data.choices[0].message.content).toContain('operations');
    });

    it('should handle GPT-4 API errors gracefully', async () => {
      const mockResponse = {
        ok: false,
        status: 429,
        statusText: 'Too Many Requests',
        json: jest.fn().mockResolvedValue({
          error: { message: 'Rate limit exceeded' },
        }),
      };

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue(mockResponse as any);

      const response = await fetch('https://api.openai.com/v1/chat/completions', {
        method: 'POST',
        headers: { 'Authorization': 'Bearer test-key' },
        body: JSON.stringify({}),
      });

      expect(response.ok).toBe(false);
      expect(response.status).toBe(429);
    });

    it('should strip secrets from prompts', () => {
      const stripSecrets = (text: string): string => {
        return text
          .replace(/sk-[a-zA-Z0-9]{48}/g, '[REDACTED_API_KEY]')
          .replace(/ghp_[a-zA-Z0-9]{36}/g, '[REDACTED_GITHUB_TOKEN]')
          .replace(/password[:\s]*[^\s\n]+/gi, 'password: [REDACTED]')
          .replace(/token[:\s]*[^\s\n]+/gi, 'token: [REDACTED]');
      };

      const unsafePrompt = `
        Here's my API key: sk-1234567890abcdef1234567890abcdef12345678
        And my GitHub token: ghp_abcdefghijklmnopqrstuvwxyz123456
        Also password: mysecretpassword
        And token: bearer-token-123
      `;

      const safePrompt = stripSecrets(unsafePrompt);

      expect(safePrompt).not.toContain('sk-1234567890abcdef1234567890abcdef12345678');
      expect(safePrompt).not.toContain('ghp_abcdefghijklmnopqrstuvwxyz123456');
      expect(safePrompt).not.toContain('mysecretpassword');
      expect(safePrompt).not.toContain('bearer-token-123');
      expect(safePrompt).toContain('[REDACTED_API_KEY]');
      expect(safePrompt).toContain('[REDACTED_GITHUB_TOKEN]');
      expect(safePrompt).toContain('[REDACTED]');
    });
  });

  describe('Claude Integration', () => {
    it('should successfully call Claude API', async () => {
      const mockResponse = {
        ok: true,
        json: jest.fn().mockResolvedValue({
          content: [{ text: '{"score": 0.92, "feedback": "Good patch", "suggestions": [], "isAcceptable": true}' }],
          usage: { input_tokens: 50, output_tokens: 30 },
        }),
      };

      (fetch as jest.MockedFunction<typeof fetch>).mockResolvedValue(mockResponse as any);

      const response = await fetch('https://api.anthropic.com/v1/messages', {
        method: 'POST',
        headers: {
          'x-api-key': 'test-key',
          'Content-Type': 'application/json',
          'anthropic-version': '2023-06-01',
        },
        body: JSON.stringify({
          model: 'claude-3-sonnet-20240229',
          max_tokens: 1500,
          temperature: 0.3,
          messages: [{ role: 'user', content: 'Test critique prompt' }],
        }),
      });

      expect(response.ok).toBe(true);
      expect(fetch).toHaveBeenCalledWith(
        'https://api.anthropic.com/v1/messages',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'x-api-key': 'test-key',
            'anthropic-version': '2023-06-01',
          }),
        })
      );
    });
  });

  describe('Cost and Time Guards', () => {
    it('should enforce cost limits', () => {
      const MAX_COST_USD = 3.0;
      const COST_PER_TOKEN_GPT4 = 0.00003;
      
      let totalCost = 0;
      
      const addCost = (tokens: number) => {
        const cost = tokens * COST_PER_TOKEN_GPT4;
        totalCost += cost;
        
        if (totalCost > MAX_COST_USD) {
          throw new Error('Cost limit exceeded');
        }
        
        return cost;
      };

      // Should not throw for reasonable usage
      expect(() => addCost(10000)).not.toThrow(); // $0.30
      expect(() => addCost(50000)).not.toThrow(); // $1.50 (total $1.80)
      
      // Should throw when exceeding limit
      expect(() => addCost(50000)).toThrow('Cost limit exceeded'); // Would be $3.30 total
    });

    it('should enforce time limits', () => {
      const MAX_EXECUTION_TIME = 180000; // 3 minutes
      const startTime = Date.now();
      
      const checkTimeLimit = () => {
        const elapsed = Date.now() - startTime;
        if (elapsed > MAX_EXECUTION_TIME) {
          throw new Error('Execution time limit exceeded');
        }
      };

      // Should not throw immediately
      expect(() => checkTimeLimit()).not.toThrow();
    });
  });

  describe('JSON Patch Generation', () => {
    it('should generate valid JSON patch operations', () => {
      const validatePatch = (patch: any): boolean => {
        if (!patch || typeof patch !== 'object') return false;
        if (!Array.isArray(patch.operations)) return false;
        
        return patch.operations.every((op: any) => {
          const validOps = ['add', 'remove', 'replace', 'move', 'copy', 'test'];
          return (
            validOps.includes(op.op) &&
            typeof op.path === 'string' &&
            op.path.startsWith('/')
          );
        });
      };

      const validPatch = {
        operations: [
          { op: 'add', path: '/newProperty', value: 'test' },
          { op: 'replace', path: '/existingProperty', value: 'updated' },
          { op: 'remove', path: '/oldProperty' },
        ],
        description: 'Test patch',
        confidence: 0.85,
      };

      const invalidPatch = {
        operations: [
          { op: 'invalid', path: 'no-slash', value: 'test' },
        ],
      };

      expect(validatePatch(validPatch)).toBe(true);
      expect(validatePatch(invalidPatch)).toBe(false);
    });
  });

  describe('Dual-Agent Loop Logic', () => {
    it('should iterate until score threshold is met', async () => {
      const SCORE_THRESHOLD = 0.95;
      const MAX_ITERATIONS = 10;
      
      let iteration = 0;
      let currentScore = 0.7;
      
      const runIteration = async (): Promise<{ score: number; patch: any }> => {
        iteration++;
        // Simulate improving score over iterations
        currentScore = Math.min(0.6 + (iteration * 0.1), 1.0);
        
        return {
          score: currentScore,
          patch: {
            operations: [{ op: 'add', path: `/iteration${iteration}`, value: 'test' }],
            description: `Iteration ${iteration}`,
            confidence: currentScore,
          },
        };
      };

      let finalResult;
      while (iteration < MAX_ITERATIONS) {
        const result = await runIteration();
        finalResult = result;
        
        if (result.score >= SCORE_THRESHOLD) {
          break;
        }
      }

      expect(iteration).toBeLessThanOrEqual(MAX_ITERATIONS);
      expect(finalResult?.score).toBeGreaterThanOrEqual(SCORE_THRESHOLD);
    });

    it('should stop at max iterations even if threshold not met', async () => {
      const SCORE_THRESHOLD = 0.95;
      const MAX_ITERATIONS = 3;
      
      let iteration = 0;
      
      const runIteration = async (): Promise<{ score: number }> => {
        iteration++;
        return { score: 0.7 }; // Always below threshold
      };

      while (iteration < MAX_ITERATIONS) {
        const result = await runIteration();
        if (result.score >= SCORE_THRESHOLD) {
          break;
        }
      }

      expect(iteration).toBe(MAX_ITERATIONS);
    });
  });

  describe('Database Integration', () => {
    it('should save session data to Supabase', async () => {
      const mockInsert = jest.fn().mockReturnValue({
        select: jest.fn().mockReturnValue({
          single: jest.fn().mockResolvedValue({
            data: { id: 'test-session-id' },
            error: null,
          }),
        }),
      });

      mockSupabase.from.mockReturnValue({ insert: mockInsert });

      const sessionData = {
        prompt: 'Test prompt',
        max_loops: 10,
        status: 'running',
      };

      // Simulate session creation
      const result = mockSupabase.from('reactor_sessions').insert(sessionData).select().single();

      expect(mockSupabase.from).toHaveBeenCalledWith('reactor_sessions');
      expect(mockInsert).toHaveBeenCalledWith(sessionData);
    });

    it('should save agent logs to Supabase', async () => {
      const mockInsert = jest.fn().mockResolvedValue({ error: null });
      mockSupabase.from.mockReturnValue({ insert: mockInsert });

      const logEntry = {
        session_id: 'test-session-id',
        iteration: 1,
        plan: 'Test plan',
        critique: 'Test critique',
        score: 0.85,
        patch: { operations: [] },
      };

      // Simulate log insertion
      mockSupabase.from('agent_logs').insert(logEntry);

      expect(mockSupabase.from).toHaveBeenCalledWith('agent_logs');
      expect(mockInsert).toHaveBeenCalledWith(logEntry);
    });
  });

  describe('Error Handling', () => {
    it('should handle API failures gracefully', async () => {
      (fetch as jest.MockedFunction<typeof fetch>).mockRejectedValue(new Error('Network error'));

      const handleAPICall = async () => {
        try {
          await fetch('https://api.openai.com/v1/chat/completions');
          return { success: true };
        } catch (error) {
          return { success: false, error: error instanceof Error ? error.message : 'Unknown error' };
        }
      };

      const result = await handleAPICall();
      expect(result.success).toBe(false);
      expect(result.error).toBe('Network error');
    });

    it('should provide fallback responses when AI APIs fail', () => {
      const generateFallbackPatch = (prompt: string) => ({
        operations: [
          {
            op: 'add',
            path: '/fallback',
            value: { prompt, timestamp: new Date().toISOString() }
          }
        ],
        description: `Fallback patch for: ${prompt}`,
        confidence: 0.6,
      });

      const fallback = generateFallbackPatch('test prompt');
      
      expect(fallback.operations).toHaveLength(1);
      expect(fallback.operations[0].op).toBe('add');
      expect(fallback.confidence).toBe(0.6);
      expect(fallback.description).toContain('test prompt');
    });
  });
});
